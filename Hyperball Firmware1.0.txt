/**
 * HyperBall Future-Proof Firmware v2.0
 * Designed for XIAO nRF52840 with LSM6DS3 IMU
 * Integrated with Madgwick AHRS for quaternion-based orientation
 */

#include <ArduinoBLE.h>
#include <LSM6DS3.h>  // Using the Seeed LSM6DS3 library instead of Arduino_LSM6DS3
#include <Wire.h>
#include <SPI.h>
#include <MadgwickAHRS.h>  // Added for orientation filtering

// ===== VERSION CONSTANTS =====
#define PROTOCOL_VERSION_MAJOR 2
#define PROTOCOL_VERSION_MINOR 0
#define FIRMWARE_VERSION "2.0.0"

// ===== PHYSICAL CONSTANTS =====
const float LANE_LENGTH = 18.29f; // Standard bowling lane length in meters (60 feet)

// ===== PIN DEFINITIONS =====
const int redLedPin = LEDR;    // BLE status (on when advertising, off when connected)
const int greenLedPin = LEDG;  // Ready/Data Ready status
const int blueLedPin = LEDB;   // Rolling status

// ===== SENSOR CONFIGURATION =====
const int SENSOR_ID = 0;  // Change for each device (0-4)

// ===== FEATURE FLAGS =====
#define FEATURE_RAW_DATA          0x00000001
#define FEATURE_ADVANCED_METRICS  0x00000002
#define FEATURE_BATTERY_MONITOR   0x00000004
#define FEATURE_FIRMWARE_UPDATE   0x00000008
#define FEATURE_MULTI_SENSOR_SYNC 0x00000010
#define FEATURE_SHOT_STORAGE      0x00000020
#define FEATURE_CALIBRATION       0x00000040
#define FEATURE_QUATERNION        0x00000080  // New feature flag for quaternion output
#define FEATURE_POWER_SAVE        0x00000100  // New feature flag for power saving

// ===== ERROR CODES =====
enum ErrorCode {
  ERROR_NONE = 0,
  ERROR_IMU_INIT_FAILED = 1,
  ERROR_BLE_INIT_FAILED = 2,
  ERROR_IMU_COMMUNICATION = 3,
  ERROR_LOW_BATTERY = 4,
  ERROR_MEMORY_FULL = 5,
  ERROR_CONFIG_INIT_FAILED = 6,
  ERROR_CALIBRATION_FAILED = 7,
  ERROR_BLE_ADVERTISING_FAILED = 8,  // New error code
  ERROR_BLE_CONNECTION_LOST = 9      // New error code
};

// ===== STATE MACHINE =====
enum State {
  STATE_INITIALIZING,
  STATE_NOT_ROLLING,
  STATE_READY,
  STATE_ROLLING,
  STATE_DATA_READY,
  STATE_ERROR,
  STATE_CALIBRATING,
  STATE_FIRMWARE_UPDATE,
  STATE_POWER_SAVE              // New state for power saving
};

// ===== COMMAND DEFINITIONS =====
#define CMD_RESET                 0x00
#define CMD_SEND_LATEST           0x01
#define CMD_SEND_SPECIFIC         0x02
#define CMD_CLEAR_ALL             0x03
#define CMD_DEBUG_MODE            0x04
#define CMD_CONFIG_UPDATE         0x05
#define CMD_CALIBRATE             0x06
#define CMD_GET_RAW_DATA          0x07
#define CMD_ENTER_POWER_SAVE      0x08  // New command for power saving
#define CMD_EXIT_POWER_SAVE       0x09  // New command to exit power saving
#define CMD_SELF_TEST             0x0A  // New command for self-test

// Test mode commands
#define CMD_TEST_MODE_ENTER       0x80
#define CMD_TEST_MODE_EXIT        0x81
#define CMD_SIMULATE_RELEASE      0x82
#define CMD_SIMULATE_IMPACT       0x83
#define CMD_SIMULATE_COMPLETE_SHOT 0x84
#define CMD_TEST_LEDS             0x85
#define CMD_SIMULATE_ERROR        0x86

// ===== DATA STRUCTURES =====

// Shot data structure (Version 1)
struct ShotData {
  float speed;      // mph
  float rpm;        // revolutions per minute
  float tilt;       // degrees
  float impact;     // g-force
  int shotNumber;   // Sequential shot number
  int sensorId;     // Identifies which sensor this shot came from
  // Added quaternion data to internal storage
  float quatW;      // Quaternion W component
  float quatX;      // Quaternion X component
  float quatY;      // Quaternion Y component
  float quatZ;      // Quaternion Z component
};

// Extended shot data structure (Version 2)
struct ExtendedShotData {
  uint8_t formatVersion;  // 1 byte for format version
  float speed;            // 4 bytes
  float rpm;              // 4 bytes
  float tilt;             // 4 bytes
  float impact;           // 4 bytes
  int shotNumber;         // 4 bytes
  int sensorId;           // 4 bytes
  float revRate;          // 4 bytes
  float axisRotation;     // 4 bytes
  float quatW;            // 4 bytes (new)
  float quatX;            // 4 bytes (new)
  float quatY;            // 4 bytes (new)
  float quatZ;            // 4 bytes (new)
  uint8_t flags;          // 1 byte for boolean flags
  uint8_t reserved[3];    // 3 bytes reserved for future use
};

// Sensor configuration structure
struct SensorConfig {
  float releaseThreshold;  // Acceleration threshold for release detection (g)
  float impactThreshold;   // Acceleration threshold for impact detection (g)
  float rollThreshold;     // Gyro threshold for roll detection (dps)
  int sampleRate;          // Sample rate during rolling (Hz)
  int idleSampleRate;      // Sample rate when idle (Hz)
  float madgwickBeta;      // Madgwick filter beta parameter
  unsigned long rollingTimeout; // Timeout for rolling state (ms)
  bool autoResetEnabled;   // Auto-reset after data ready state
  uint8_t reserved[3];     // Reserved for future use (reduced from 8 to accommodate new fields)
};

// Feature flags structure
struct FeatureFlags {
  uint32_t supportedFeatures;  // Bitfield of supported features
  uint32_t enabledFeatures;    // Bitfield of currently enabled features
};

// Raw data structure
struct RawSensorData {
  float accelX;
  float accelY;
  float accelZ;
  float gyroX;
  float gyroY;
  float gyroZ;
  uint32_t timestamp;
  uint8_t reserved[4];
};

// Command structure
struct Command {
  uint8_t commandId;     // Basic command identifier
  uint8_t subCommand;    // Sub-command for extended functionality
  uint16_t parameter;    // Optional parameter (16-bit)
};

// ===== GLOBAL VARIABLES =====
// Using the Seeed_Arduino_LSM6DS3 library
LSM6DS3 imu(I2C_MODE, 0x6A);    // Try 0x6B if 0x6A doesn't work
Madgwick madgwick;              // Madgwick AHRS filter

// Default configuration
SensorConfig config = {
  .releaseThreshold = 0.3f,    // g
  .impactThreshold = 3.0f,     // g
  .rollThreshold = 50.0f,      // dps
  .sampleRate = 416,           // Hz
  .idleSampleRate = 104,       // Hz
  .madgwickBeta = 0.1f,        // Madgwick beta parameter
  .reserved = {0}
};

State currentState = STATE_INITIALIZING;
BLEService ballService("180A");

// Standard characteristics (backward compatible)
BLEIntCharacteristic stateCharacteristic("2A00", BLERead | BLEWrite | BLENotify);
BLEByteCharacteristic commandCharacteristic("2A01", BLERead | BLEWrite);
BLECharacteristic metricsCharacteristic("2A02", BLERead | BLENotify, 24); // V1 format
BLEIntCharacteristic shotCountCharacteristic("2A03", BLERead | BLENotify);
BLEIntCharacteristic shotIndexCharacteristic("2A04", BLERead | BLEWrite);

// Extended characteristics (new in V2)
BLECharacteristic versionCharacteristic("2A26", BLERead, 4);
BLECharacteristic configCharacteristic("2A05", BLERead | BLEWrite, 32);
BLECharacteristic errorCharacteristic("2A7D", BLERead | BLENotify, 2);
BLECharacteristic featureFlagsCharacteristic("2A27", BLERead, 8);
BLECharacteristic rawDataCharacteristic("2A06", BLERead | BLENotify, 32);
BLECharacteristic extendedMetricsCharacteristic("2A28", BLERead | BLENotify, 48); // Updated size for quaternion data

// Storage
const int MAX_STORED_SHOTS = 100;
ShotData storedShots[MAX_STORED_SHOTS];
int currentShotIndex = 0;
int totalStoredShots = 0;
int nextShotNumber = 1;

// Sensor data
float accel_offset[3] = {0, 0, 0};
float gyro_offset[3] = {0, 0, 0};
float quaternion[4] = {1.0f, 0.0f, 0.0f, 0.0f};  // w, x, y, z

// Motion detection
unsigned long releaseTime = 0;
float gyroSum = 0.0f;
float accelXSum = 0.0f;
int sampleCount = 0;
float tiltAtRelease = 0.0f;
unsigned long impactStartTime = 0;
float maxImpactForce = 0.0f;

// State tracking
unsigned long stateEnteredTime = 0;
bool inTestMode = false;
bool debugMode = false;
uint32_t enabledFeatures = FEATURE_SHOT_STORAGE | FEATURE_QUATERNION; // Enable quaternion by default

// Timing control
unsigned long lastUpdateMicros = 0;
unsigned long targetLoopTimeMicros = 0;

// ===== FUNCTION DECLARATIONS =====
void resetState();
void handleCommands();
void sendShotMetrics(int shotIndex);
void storeNewShot(float speed, float rpm, float tilt, float impact);
void clearAllShots();
void updateFeatureFlags();
void reportError(ErrorCode code);
void enterState(State newState);
void updateStateMachine();
void sendRawData();
void calibrateIMU(int numSamples = 100);
bool initializeBLE();
bool initializeIMU();
void blinkErrorCode(ErrorCode code);
void runSelfTest();
void enterPowerSaveMode();
void exitPowerSaveMode();

// ===== BLE EVENT HANDLERS =====
void blePeripheralConnectHandler(BLEDevice central) {
  digitalWrite(redLedPin, HIGH);  // Red off when connected
  Serial.print("Connected to central: ");
  Serial.println(central.address());
}

void blePeripheralDisconnectHandler(BLEDevice central) {
  digitalWrite(redLedPin, LOW);  // Red on when disconnected (advertising)
  Serial.print("Disconnected from central: ");
  Serial.println(central.address());

  // Check if we should enter power save mode after disconnect
  if (enabledFeatures & FEATURE_POWER_SAVE) {
    enterPowerSaveMode();
  }
}

// ===== MADGWICK FILTER FUNCTIONS =====
void beginMadgwick(float sampleFreq) {
  madgwick.begin(sampleFreq);
}

void setMadgwickBeta(float beta) {
  madgwick.setBeta(beta);
}

// Direct access to Madgwick quaternion values
float getMadgwickQuaternionW() {
  return madgwick.q0;
}

float getMadgwickQuaternionX() {
  return madgwick.q1;
}

float getMadgwickQuaternionY() {
  return madgwick.q2;
}

float getMadgwickQuaternionZ() {
  return madgwick.q3;
}

// ===== HELPER FUNCTIONS =====
void enterState(State newState) {
  // Exit actions for current state
  switch (currentState) {
    case STATE_ROLLING:
      // Stop high-frequency sampling
      targetLoopTimeMicros = 1000000 / config.idleSampleRate;
      break;

    default:
      break;
  }

  // Update state
  currentState = newState;
  stateEnteredTime = millis();

  // Enter actions for new state
  switch (newState) {
    case STATE_READY:
      stateCharacteristic.writeValue((int)STATE_READY);
      releaseTime = 0;
      gyroSum = 0.0f;
      accelXSum = 0.0f;
      sampleCount = 0;
      tiltAtRelease = 0.0f;
      impactStartTime = 0;
      maxImpactForce = 0.0f;

      // Reset quaternion to identity
      quaternion[0] = 1.0f;
      quaternion[1] = 0.0f;
      quaternion[2] = 0.0f;
      quaternion[3] = 0.0f;

      // Set to idle sample rate
      targetLoopTimeMicros = 1000000 / config.idleSampleRate;
      beginMadgwick(config.idleSampleRate);
      setMadgwickBeta(config.madgwickBeta);

      digitalWrite(redLedPin, BLE.connected() ? HIGH : LOW);
      digitalWrite(greenLedPin, LOW);    // Green on (inverted logic)
      digitalWrite(blueLedPin, HIGH);    // Blue off

      Serial.println("State reset - ready for next shot");
      break;

    case STATE_ROLLING:
      stateCharacteristic.writeValue((int)STATE_ROLLING);

      // Set to high sample rate
      targetLoopTimeMicros = 1000000 / config.sampleRate;
      beginMadgwick(config.sampleRate);

      digitalWrite(greenLedPin, HIGH);   // Green off
      digitalWrite(blueLedPin, LOW);     // Blue on

      Serial.println("ROLLING!");
      break;

    case STATE_DATA_READY:
      stateCharacteristic.writeValue((int)STATE_DATA_READY);

      // Set to idle sample rate
      targetLoopTimeMicros = 1000000 / config.idleSampleRate;

      digitalWrite(greenLedPin, LOW);   // Green on
      digitalWrite(blueLedPin, HIGH);   // Blue off

      Serial.println("DATA READY!");
      break;

    case STATE_ERROR:
      stateCharacteristic.writeValue((int)STATE_ERROR);
      digitalWrite(greenLedPin, HIGH);   // Green off
      digitalWrite(blueLedPin, HIGH);    // Blue off

      // Blink red LED to indicate error
      for (int i = 0; i < 5; i++) {
        digitalWrite(redLedPin, LOW);    // Turn on (inverted logic)
        delay(100);
        digitalWrite(redLedPin, HIGH);   // Turn off
        delay(100);
      }

      Serial.println("ERROR STATE!");
      break;

    case STATE_CALIBRATING:
      stateCharacteristic.writeValue((int)STATE_CALIBRATING);
      digitalWrite(greenLedPin, HIGH);   // Green off
      digitalWrite(blueLedPin, LOW);     // Blue on

      Serial.println("CALIBRATING...");
      break;

    case STATE_POWER_SAVE:
      stateCharacteristic.writeValue((int)STATE_POWER_SAVE);
      digitalWrite(greenLedPin, HIGH);   // Green off
      digitalWrite(blueLedPin, HIGH);    // Blue off

      // Blink all LEDs to indicate power save mode
      for (int i = 0; i < 3; i++) {
        digitalWrite(redLedPin, LOW);    // All on
        digitalWrite(greenLedPin, LOW);
        digitalWrite(blueLedPin, LOW);
        delay(100);
        digitalWrite(redLedPin, HIGH);   // All off
        digitalWrite(greenLedPin, HIGH);
        digitalWrite(blueLedPin, HIGH);
        delay(100);
      }

      Serial.println("POWER SAVE MODE");
      break;

    default:
      break;
  }
}

void resetState() {
  enterState(STATE_READY);
}

void handleCommands() {
  if (commandCharacteristic.written()) {
    uint8_t command = commandCharacteristic.value();

    if (debugMode) {
      Serial.print("Received command: 0x");
      Serial.println(command, HEX);
    }

    switch (command) {
      case CMD_RESET:
        resetState();
        break;

      case CMD_SEND_LATEST:
        if (totalStoredShots > 0) {
          int latestIndex = (currentShotIndex - 1 + MAX_STORED_SHOTS) % MAX_STORED_SHOTS;
          sendShotMetrics(latestIndex);
        } else {
          Serial.println("No shots available to send");
        }
        break;

      case CMD_SEND_SPECIFIC:
        if (totalStoredShots > 0) {
          int requestedIndex = shotIndexCharacteristic.value();

          if (requestedIndex >= 0 && requestedIndex < totalStoredShots) {
            int actualIndex = (currentShotIndex - 1 - requestedIndex + MAX_STORED_SHOTS) % MAX_STORED_SHOTS;
            sendShotMetrics(actualIndex);
          } else {
            Serial.println("Invalid shot index requested");
          }
        } else {
          Serial.println("No shots available to send");
        }
        break;

      case CMD_CLEAR_ALL:
        clearAllShots();
        break;

      case CMD_DEBUG_MODE:
        debugMode = !debugMode;
        Serial.println(debugMode ? "Debug mode ON" : "Debug mode OFF");
        break;

      case CMD_CONFIG_UPDATE:
        if (configCharacteristic.written()) {
          memcpy(&config, configCharacteristic.value(), sizeof(SensorConfig));

          // Update madgwick filter parameters
          beginMadgwick(currentState == STATE_ROLLING ? config.sampleRate : config.idleSampleRate);
          setMadgwickBeta(config.madgwickBeta);

          // Update timing
          targetLoopTimeMicros = 1000000 / (currentState == STATE_ROLLING ? config.sampleRate : config.idleSampleRate);

          Serial.println("Configuration updated");
        }
        break;

      case CMD_CALIBRATE:
        enterState(STATE_CALIBRATING);
        calibrateIMU(100); // Perform calibration with 100 samples
        enterState(STATE_READY);
        break;

      case CMD_GET_RAW_DATA:
        // Toggle raw data mode
        enabledFeatures ^= FEATURE_RAW_DATA;
        updateFeatureFlags();
        break;

      case CMD_ENTER_POWER_SAVE:
        enterPowerSaveMode();
        break;

      case CMD_EXIT_POWER_SAVE:
        exitPowerSaveMode();
        break;

      case CMD_SELF_TEST:
        runSelfTest();
        break;

      // Test mode commands
      case CMD_TEST_MODE_ENTER:
        inTestMode = true;
        Serial.println("Test mode entered");
        break;

      case CMD_TEST_MODE_EXIT:
        inTestMode = false;
        Serial.println("Test mode exited");
        break;

      case CMD_SIMULATE_RELEASE:
        if (inTestMode) {
          enterState(STATE_ROLLING);
          releaseTime = millis();
          tiltAtRelease = 12.5f; // Default simulated tilt

          // Simulate quaternion orientation
          quaternion[0] = 0.965f; // w
          quaternion[1] = 0.259f; // x
          quaternion[2] = 0.0f;   // y
          quaternion[3] = 0.0f;   // z
        }
        break;

      case CMD_SIMULATE_IMPACT:
        if (inTestMode && currentState == STATE_ROLLING) {
          // Simulate impact with default values
          maxImpactForce = 5.7f;
          float rollTime = (millis() - releaseTime) / 1000.0f;
          float speed = 15.2f; // Default simulated speed
          float rpm = 325.0f;  // Default simulated RPM

          storeNewShot(speed, rpm, tiltAtRelease, maxImpactForce);
          enterState(STATE_DATA_READY);
        }
        break;

      case CMD_SIMULATE_COMPLETE_SHOT:
        if (inTestMode) {
          // Complete shot simulation (release -> roll -> impact)
          enterState(STATE_ROLLING);
          releaseTime = millis();
          tiltAtRelease = 12.5f;

          // Simulate quaternion orientation
          quaternion[0] = 0.965f; // w
          quaternion[1] = 0.259f; // x
          quaternion[2] = 0.0f;   // y
          quaternion[3] = 0.0f;   // z

          // Simulate impact after 500ms
          delay(500);
          maxImpactForce = 5.7f;
          storeNewShot(15.2f, 325.0f, tiltAtRelease, maxImpactForce);
          enterState(STATE_DATA_READY);
        }
        break;

      case CMD_TEST_LEDS:
        // Test all LEDs
        for (int i = 0; i < 3; i++) {
          digitalWrite(redLedPin, LOW);    // Turn on (inverted logic)
          delay(200);
          digitalWrite(redLedPin, HIGH);   // Turn off
          digitalWrite(greenLedPin, LOW);  // Turn on
          delay(200);
          digitalWrite(greenLedPin, HIGH); // Turn off
          digitalWrite(blueLedPin, LOW);   // Turn on
          delay(200);
          digitalWrite(blueLedPin, HIGH);  // Turn off
        }
        break;

      case CMD_SIMULATE_ERROR:
        if (inTestMode) {
          uint8_t errorCode = 1; // Default to IMU error
          reportError((ErrorCode)errorCode);
          enterState(STATE_ERROR);
        }
        break;

      default:
        Serial.print("Unknown command: ");
        Serial.println(command);
        break;
    }
  }
}

void sendShotMetrics(int shotIndex) {
  if (shotIndex >= 0 && shotIndex < MAX_STORED_SHOTS) {
    // Send in V1 format (24 bytes) - backward compatibility
    struct {
      float speed;
      float rpm;
      float tilt;
      float impact;
      int shotNumber;
      int sensorId;
    } metrics = {
      storedShots[shotIndex].speed,
      storedShots[shotIndex].rpm,
      storedShots[shotIndex].tilt,
      storedShots[shotIndex].impact,
      storedShots[shotIndex].shotNumber,
      storedShots[shotIndex].sensorId
    };

    metricsCharacteristic.writeValue((uint8_t*)&metrics, sizeof(metrics));

    // Send extended format if feature is enabled
    if (enabledFeatures & FEATURE_ADVANCED_METRICS) {
      ExtendedShotData extendedMetrics;
      extendedMetrics.formatVersion = 2;
      extendedMetrics.speed = metrics.speed;
      extendedMetrics.rpm = metrics.rpm;
      extendedMetrics.tilt = metrics.tilt;
      extendedMetrics.impact = metrics.impact;
      extendedMetrics.shotNumber = metrics.shotNumber;
      extendedMetrics.sensorId = metrics.sensorId;

      // Calculate additional metrics
      extendedMetrics.revRate = metrics.rpm / 60.0f; // rev/sec
      extendedMetrics.axisRotation = 0.0f; // Advanced calculation would go here

      // Include quaternion data
      extendedMetrics.quatW = storedShots[shotIndex].quatW;
      extendedMetrics.quatX = storedShots[shotIndex].quatX;
      extendedMetrics.quatY = storedShots[shotIndex].quatY;
      extendedMetrics.quatZ = storedShots[shotIndex].quatZ;

      extendedMetrics.flags = 0;
      memset(extendedMetrics.reserved, 0, sizeof(extendedMetrics.reserved));

      extendedMetricsCharacteristic.writeValue((uint8_t*)&extendedMetrics, sizeof(extendedMetrics));
    }

    if (debugMode) {
      Serial.println("==== Shot Data Transferred ====");
      Serial.print("Shot #: ");
      Serial.println(metrics.shotNumber);
      Serial.print("Sensor ID: ");
      Serial.println(metrics.sensorId);
      Serial.print("Speed: ");
      Serial.print(metrics.speed);
      Serial.println(" mph");
      Serial.print("RPM: ");
      Serial.print(metrics.rpm);
      Serial.println(" rpm");
      Serial.print("Tilt: ");
      Serial.print(metrics.tilt);
      Serial.println("°");
      Serial.print("Impact: ");
      Serial.print(metrics.impact);
      Serial.println(" g");

      if (enabledFeatures & FEATURE_QUATERNION) {
        Serial.print("Quaternion (w,x,y,z): ");
        Serial.print(storedShots[shotIndex].quatW); Serial.print(", ");
        Serial.print(storedShots[shotIndex].quatX); Serial.print(", ");
        Serial.print(storedShots[shotIndex].quatY); Serial.print(", ");
        Serial.println(storedShots[shotIndex].quatZ);
      }

      Serial.println("==============================");
    }
  }
}

void storeNewShot(float speed, float rpm, float tilt, float impact) {
  storedShots[currentShotIndex].speed = speed;
  storedShots[currentShotIndex].rpm = rpm;
  storedShots[currentShotIndex].tilt = tilt;
  storedShots[currentShotIndex].impact = impact;
  storedShots[currentShotIndex].shotNumber = nextShotNumber++;
  storedShots[currentShotIndex].sensorId = SENSOR_ID;

  // Store quaternion orientation
  storedShots[currentShotIndex].quatW = quaternion[0];
  storedShots[currentShotIndex].quatX = quaternion[1];
  storedShots[currentShotIndex].quatY = quaternion[2];
  storedShots[currentShotIndex].quatZ = quaternion[3];

  currentShotIndex = (currentShotIndex + 1) % MAX_STORED_SHOTS;
  if (totalStoredShots < MAX_STORED_SHOTS) {
    totalStoredShots++;
  }

  shotCountCharacteristic.writeValue(totalStoredShots);

  if (debugMode) {
    Serial.print("Stored new shot #");
    Serial.println(nextShotNumber - 1);
  }
}

void clearAllShots() {
  totalStoredShots = 0;
  currentShotIndex = 0;
  nextShotNumber = 1;
  shotCountCharacteristic.writeValue(totalStoredShots);

  if (debugMode) {
    Serial.println("All shots cleared");
  }
}

void updateFeatureFlags() {
  FeatureFlags flags;

  // Set supported features based on firmware version
  flags.supportedFeatures = FEATURE_RAW_DATA |
                           FEATURE_ADVANCED_METRICS |
                           FEATURE_SHOT_STORAGE |
                           FEATURE_QUATERNION |      // Added quaternion feature
                           FEATURE_CALIBRATION;      // Added calibration feature

  // Set enabled features
  flags.enabledFeatures = enabledFeatures;

  featureFlagsCharacteristic.writeValue((uint8_t*)&flags, sizeof(flags));
}

void reportError(ErrorCode code) {
  uint8_t errorData[2] = {code, 0}; // Error code and reserved byte
  errorCharacteristic.writeValue(errorData, 2);

  Serial.print("ERROR: ");
  Serial.println(code);
}

void sendRawData() {
  if (enabledFeatures & FEATURE_RAW_DATA) {
    RawSensorData rawData;

    // Read accelerometer data if available
    if (IMU.accelerationAvailable()) {
      IMU.readAcceleration(rawData.accelX, rawData.accelY, rawData.accelZ);

      // Apply calibration offsets
      rawData.accelX -= accel_offset[0];
      rawData.accelY -= accel_offset[1];
      rawData.accelZ -= accel_offset[2];
    } else {
      rawData.accelX = rawData.accelY = rawData.accelZ = 0.0f;
    }

    // Read gyroscope data if available
    if (IMU.gyroscopeAvailable()) {
      IMU.readGyroscope(rawData.gyroX, rawData.gyroY, rawData.gyroZ);

      // Apply calibration offsets
      rawData.gyroX -= gyro_offset[0];
      rawData.gyroY -= gyro_offset[1];
      rawData.gyroZ -= gyro_offset[2];
    } else {
      rawData.gyroX = rawData.gyroY = rawData.gyroZ = 0.0f;
    }

    rawData.timestamp = millis();
    memset(rawData.reserved, 0, sizeof(rawData.reserved));

    rawDataCharacteristic.writeValue((uint8_t*)&rawData, sizeof(rawData));
  }
}

void updateStateMachine() {
  // Check IMU communication
  if (currentState != STATE_ERROR &&
      currentState != STATE_INITIALIZING &&
      currentState != STATE_CALIBRATING) {

    if (IMU.accelerationAvailable()) {
      float ax, ay, az;
      IMU.readAcceleration(ax, ay, az);
      if (ax == 0.0f && ay == 0.0f && az == 0.0f) {
        reportError(ERROR_IMU_COMMUNICATION);
        enterState(STATE_ERROR);
        return;
      }
    }
  }

  // Handle state transitions
  switch (currentState) {
    case STATE_INITIALIZING:
      enterState(STATE_READY);
      break;

    case STATE_READY:
    case STATE_NOT_ROLLING:
      {
        float ax = 0, ay = 0, az = 0;
        float gx = 0, gy = 0, gz = 0;

        if (IMU.accelerationAvailable() && IMU.gyroscopeAvailable()) {
          IMU.readAcceleration(ax, ay, az);
          IMU.readGyroscope(gx, gy, gz);

          // Apply calibration offsets
          ax -= accel_offset[0];
          ay -= accel_offset[1];
          az -= accel_offset[2];

          gx -= gyro_offset[0];
          gy -= gyro_offset[1];
          gz -= gyro_offset[2];

          // Update Madgwick filter (even when idle, to maintain orientation)
          madgwick.updateIMU(gx, gy, gz, ax, ay, az);

          // Get quaternion from filter
          quaternion[0] = getMadgwickQuaternionW();
          quaternion[1] = getMadgwickQuaternionX();
          quaternion[2] = getMadgwickQuaternionY();
          quaternion[3] = getMadgwickQuaternionZ();

          float accelMag = sqrt(ax * ax + ay * ay + az * az);

          if (accelMag > config.releaseThreshold) {
            enterState(STATE_ROLLING);
            releaseTime = millis();
            gyroSum = 0.0f;
            accelXSum = 0.0f;
            sampleCount = 0;
            tiltAtRelease = acos(az / accelMag) * 180.0f / PI;
            impactStartTime = 0;
            maxImpactForce = 0.0f;
          }
        }
      }
      break;

    case STATE_ROLLING:
      {
        float ax = 0, ay = 0, az = 0;
        float gx = 0, gy = 0, gz = 0;

        if (IMU.accelerationAvailable() && IMU.gyroscopeAvailable()) {
          IMU.readAcceleration(ax, ay, az);
          IMU.readGyroscope(gx, gy, gz);

          // Apply calibration offsets
          ax -= accel_offset[0];
          ay -= accel_offset[1];
          az -= accel_offset[2];

          gx -= gyro_offset[0];
          gy -= gyro_offset[1];
          gz -= gyro_offset[2];

          // Update Madgwick filter
          madgwick.updateIMU(gx, gy, gz, ax, ay, az);

          // Get quaternion from filter
          quaternion[0] = getMadgwickQuaternionW();
          quaternion[1] = getMadgwickQuaternionX();
          quaternion[2] = getMadgwickQuaternionY();
          quaternion[3] = getMadgwickQuaternionZ();

          // Send raw data if enabled
          if (enabledFeatures & FEATURE_RAW_DATA) {
            sendRawData();
          }

          // Accumulate data for calculations
          accelXSum += ax;
          gyroSum += gz;
          sampleCount++;

          // Check for impact
          float accelMag = sqrt(ax * ax + ay * ay + az * az);
          if (accelMag > config.impactThreshold) {
            if (impactStartTime == 0) {
              impactStartTime = millis();
              maxImpactForce = accelMag;
            } else if (millis() - impactStartTime <= 100) {
              // Track peak impact force over 100ms window
              maxImpactForce = max(maxImpactForce, accelMag);
            } else {
              // After 100ms window, calculate metrics and transition to DATA_READY
              float rollTime = (millis() - releaseTime) / 1000.0f;
              float avgDecel = (sampleCount > 0) ? (accelXSum / sampleCount) * 9.81f : 0.0f; // m/s²
              float speedMps = (LANE_LENGTH / rollTime) + (0.5f * avgDecel * rollTime); // Adjusted for deceleration
              float speedMph = max(speedMps * 2.23694f, 0.0f); // Convert to mph, ensure non-negative
              float rpm = (fabs(gyroSum) / 360.0f) * 60.0f / rollTime; // Calculate RPM

              // Store and transition state
              storeNewShot(speedMph, rpm, tiltAtRelease, maxImpactForce);
              enterState(STATE_DATA_READY);
            }
          }

          // Check for timeout
          if (millis() - releaseTime > config.rollingTimeout) {
            // Calculate metrics with what we have
            float rollTime = config.rollingTimeout / 1000.0f;
            float avgDecel = (sampleCount > 0) ? (accelXSum / sampleCount) * 9.81f : 0.0f;
            float speedMps = (LANE_LENGTH / rollTime) + (0.5f * avgDecel * rollTime);
            float speedMph = max(speedMps * 2.23694f, 0.0f);
            float rpm = (fabs(gyroSum) / 360.0f) * 60.0f / rollTime;

            // Use last known impact or default to 0
            storeNewShot(speedMph, rpm, tiltAtRelease, maxImpactForce > 0 ? maxImpactForce : 0.0f);
            enterState(STATE_DATA_READY);
          }
        }
      }
      break;

    case STATE_DATA_READY:
      // Auto-reset after delay if configured
      if (config.autoResetEnabled && millis() - stateEnteredTime > 1000) {
        enterState(STATE_READY);
      }
      break;

    case STATE_ERROR:
      // Stay in error state until explicit reset
      break;

    case STATE_CALIBRATING:
      // Calibration is handled directly in the command handler
      break;

    case STATE_FIRMWARE_UPDATE:
      // Firmware update logic would go here
      break;
  }
}

void setup() {
  Serial.begin(115200);  // Increased baud rate for better performance
  #ifdef DEBUG
  while (!Serial);
  #endif

  Serial.println("HyperBall sensor v2.0 starting up...");

  // Initialize pins
  pinMode(redLedPin, OUTPUT);
  pinMode(greenLedPin, OUTPUT);
  pinMode(blueLedPin, OUTPUT);

  // Turn all LEDs on during initialization
  digitalWrite(redLedPin, LOW);    // On (inverted logic)
  digitalWrite(greenLedPin, LOW);  // On (inverted logic)
  digitalWrite(blueLedPin, LOW);   // On (inverted logic)

  // Initialize IMU
  Serial.println("Initializing IMU...");
  if (!IMU.begin()) {
    Serial.println("Failed to initialize IMU!");
    reportError(ERROR_IMU_INIT_FAILED);
    enterState(STATE_ERROR);
    while (1) {
      digitalWrite(redLedPin, LOW);  // On (inverted logic)
      delay(100);
      digitalWrite(redLedPin, HIGH); // Off
      delay(100);
    }
  }
  Serial.println("IMU initialized successfully");

  // Initialize BLE
  Serial.println("Initializing BLE...");
  if (!BLE.begin()) {
    Serial.println("Starting BLE failed!");
    reportError(ERROR_BLE_INIT_FAILED);
    enterState(STATE_ERROR);
    while (1) {
      digitalWrite(redLedPin, LOW);  // On (inverted logic)
      delay(100);
      digitalWrite(redLedPin, HIGH); // Off
      delay(100);
    }
  }
  Serial.println("BLE initialized successfully");

  // Set BLE name with sensor ID
  char bleName[20];
  sprintf(bleName, "HyperBall_%d", SENSOR_ID);
  BLE.setLocalName(bleName);
  BLE.setAdvertisedService(ballService);

  // Add characteristics to service
  ballService.addCharacteristic(stateCharacteristic);
  ballService.addCharacteristic(commandCharacteristic);
  ballService.addCharacteristic(metricsCharacteristic);
  ballService.addCharacteristic(shotCountCharacteristic);
  ballService.addCharacteristic(shotIndexCharacteristic);

  // Extended characteristics (V2)
  ballService.addCharacteristic(versionCharacteristic);
  ballService.addCharacteristic(configCharacteristic);
  ballService.addCharacteristic(errorCharacteristic);
  ballService.addCharacteristic(featureFlagsCharacteristic);
  ballService.addCharacteristic(rawDataCharacteristic);
  ballService.addCharacteristic(extendedMetricsCharacteristic);

  BLE.addService(ballService);
  BLE.setEventHandler(BLEConnected, blePeripheralConnectHandler);
  BLE.setEventHandler(BLEDisconnected, blePeripheralDisconnectHandler);

  // Initialize protocol version
  uint8_t versionData[4] = {PROTOCOL_VERSION_MAJOR & 0xFF,
                           (PROTOCOL_VERSION_MAJOR >> 8) & 0xFF,
                           PROTOCOL_VERSION_MINOR & 0xFF,
                           (PROTOCOL_VERSION_MINOR >> 8) & 0xFF};
  versionCharacteristic.writeValue(versionData, 4);

  // Initialize feature flags
  updateFeatureFlags();

  // Initialize configuration
  configCharacteristic.writeValue((uint8_t*)&config, sizeof(config));

  // Initialize error status
  reportError(ERROR_NONE);

  // Initialize Madgwick filter
  beginMadgwick(config.idleSampleRate);
  setMadgwickBeta(config.madgwickBeta);

  // Set initial target loop time
  targetLoopTimeMicros = 1000000 / config.idleSampleRate;

  // Start advertising
  BLE.advertise();
  Serial.println("BLE advertising started - device is now discoverable as:");
  Serial.print("  Device name: ");
  Serial.println(bleName);
  Serial.print("  MAC address: ");
  Serial.println(BLE.address());

  // Perform initial calibration
  Serial.println("Performing initial calibration...");
  calibrateIMU(100);

  // Initialize LEDs based on state
  resetState();

  Serial.print("Setup complete for sensor ");
  Serial.println(SENSOR_ID);
  Serial.println("Ready to detect bowling motion");

  lastUpdateMicros = micros();
}

void loop() {
  // Record loop start time
  unsigned long loopStartTime = micros();

  // Poll BLE events
  BLE.poll();

  // Handle incoming commands
  handleCommands();

  // Update state machine
  updateStateMachine();

  // Heartbeat debug output
  static unsigned long lastHeartbeat = 0;
  if (debugMode && millis() - lastHeartbeat > 5000) {
    lastHeartbeat = millis();

    Serial.print("Status: ");
    if (BLE.connected()) {
      Serial.println("Connected to central device");
    } else {
      Serial.println("Advertising - waiting for connection");
    }

    Serial.print("Current state: ");
    switch (currentState) {
      case STATE_INITIALIZING: Serial.println("INITIALIZING"); break;
      case STATE_NOT_ROLLING: Serial.println("NOT_ROLLING"); break;
      case STATE_READY: Serial.println("READY"); break;
      case STATE_ROLLING: Serial.println("ROLLING"); break;
      case STATE_DATA_READY: Serial.println("DATA_READY"); break;
      case STATE_ERROR: Serial.println("ERROR"); break;
      case STATE_CALIBRATING: Serial.println("CALIBRATING"); break;
      case STATE_FIRMWARE_UPDATE: Serial.println("FIRMWARE_UPDATE"); break;
    }

    Serial.print("Total stored shots: ");
    Serial.println(totalStoredShots);

    // Check IMU readings
    float ax = 0, ay = 0, az = 0;
    if (IMU.accelerationAvailable()) {
      IMU.readAcceleration(ax, ay, az);
    }
    float accelMag = sqrt(ax * ax + ay * ay + az * az);

    Serial.print("Accel (g): X=");
    Serial.print(ax);
    Serial.print(", Y=");
    Serial.print(ay);
    Serial.print(", Z=");
    Serial.print(az);
    Serial.print(", Mag=");
    Serial.println(accelMag);

    float gx = 0, gy = 0, gz = 0;
    if (IMU.gyroscopeAvailable()) {
      IMU.readGyroscope(gx, gy, gz);
    }

    Serial.print("Gyro (dps): X=");
    Serial.print(gx);
    Serial.print(", Y=");
    Serial.print(gy);
    Serial.print(", Z=");
    Serial.println(gz);

    Serial.print("Quaternion: W=");
    Serial.print(quaternion[0]);
    Serial.print(", X=");
    Serial.print(quaternion[1]);
    Serial.print(", Y=");
    Serial.print(quaternion[2]);
    Serial.print(", Z=");
    Serial.println(quaternion[3]);

    Serial.println();
  }

  // Send raw data periodically if enabled
  static unsigned long lastRawData = 0;
  if ((enabledFeatures & FEATURE_RAW_DATA) && millis() - lastRawData > 100) {
    lastRawData = millis();
    sendRawData();
  }

  // Ensure consistent timing
  unsigned long processingTime = micros() - loopStartTime;
  if (processingTime < targetLoopTimeMicros) {
    delayMicroseconds(targetLoopTimeMicros - processingTime);
  }
}
